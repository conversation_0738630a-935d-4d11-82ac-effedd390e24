const Complaint = require('../models/Complaint');

exports.submitComplaint = async (req, res) => {
  const { message } = req.body;
  const fromUserId = req.user.id;
  try {
    const complaint = new Complaint({ fromUserId, message });
    await complaint.save();
    res.status(201).json(complaint);
  } catch (err) {
    res.status(500).json({ message: 'Failed to submit complaint', error: err });
  }
};

exports.replyToComplaint = async (req, res) => {
  const { id } = req.params;
  const { reply } = req.body;
  try {
    const complaint = await Complaint.findById(id);
    if (!complaint) return res.status(404).json({ message: 'Complaint not found' });
    complaint.reply = reply;
    complaint.status = 'closed';
    await complaint.save();
    res.json(complaint);
  } catch (err) {
    res.status(500).json({ message: 'Failed to reply to complaint', error: err });
  }
};

exports.getAllComplaints = async (req, res) => {
  try {
    const complaints = await Complaint.find().populate('fromUserId', 'name phone');
    res.json(complaints);
  } catch (err) {
    res.status(500).json({ message: 'Failed to fetch complaints', error: err });
  }
};