const rateLimit = require('express-rate-limit');
const mongoSanitize = require('express-mongo-sanitize');
const xss = require('xss-clean');

// حد معدل الطلبات
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: 100, // حد أقصى 100 طلب لكل IP
  message: {
    error: 'تم تجاوز الحد المسموح من الطلبات، حاول مرة أخرى لاحق<|im_start|>'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// حد خاص لنموذج الاتصال
const contactLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // ساعة واحدة
  max: 5, // 5 رسائل كحد أقصى في الساعة
  message: {
    error: 'تم إرسال عدد كبير من الرسائل، حاول مرة أخرى بعد ساعة'
  }
});

const securityMiddleware = [
  limiter,
  mongoSanitize(), // منع NoSQL injection
  xss(), // منع XSS attacks
];

module.exports = { securityMiddleware, contactLimiter };