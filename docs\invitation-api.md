## Invitation Routes (/invitations)

### GET /invitations
**الوصف:** قائمة الدعوات
**Query Parameters:**
- eventId: ObjectId (optional)
- status: string (optional)

### POST /invitations
**المعاملات المرسلة:**
```json
{
  "eventId": "ObjectId (required)",
  "guestName": "string (required)",
  "guestPhone": "string (optional)",
  "message": "string (optional)"
}
```

### GET /invitations/:id
**URL Parameters:** id (ObjectId)

### PUT /invitations/:id
**URL Parameters:** id (ObjectId)
**المعاملات المرسلة:**
```json
{
  "guestName": "string",
  "guestPhone": "string",
  "message": "string"
}
```

### DELETE /invitations/:id
**URL Parameters:** id (ObjectId)

### GET /invitations/:id/qr
**URL Parameters:** id (ObjectId)
**الوصف:** تحميل QR Code
**الاستجابة:** Image file (PNG)

### POST /invitations/bulk
**المعاملات المرسلة:**
```json
{
  "eventId": "ObjectId (required)",
  "guests": [
    {
      "name": "string (required)",
      "phone": "string (optional)"
    }
  ]
}
```