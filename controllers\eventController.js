const Event = require('../models/Event');

exports.createEvent = async (req, res) => {
  const { hallId, eventName, eventDate } = req.body;

  try {
    const conflict = await Event.findOne({ hallId, eventDate });
    if (conflict) return res.status(400).json({ message: 'Date already booked for this hall' });

    const newEvent = new Event({
      hallId,
      eventName,
      eventDate,
      clientId: req.user.id
    });

    await newEvent.save();
    res.status(201).json(newEvent);
  } catch (err) {
    res.status(500).json({ message: 'Failed to create event', error: err });
  }
};

exports.getEventsByHall = async (req, res) => {
  try {
    const events = await Event.find({ hallId: req.params.hallId });
    res.json(events);
  } catch (err) {
    res.status(500).json({ message: 'Failed to fetch events', error: err });
  }
};

exports.listManagerEvents = async (req, res) => {
  try {
    const events = await Event.find({ manager: req.user.id });
    res.json({ events });
  } catch (err) {
    res.status(500).send('خطأ في تحميل المناسبات');
  }
};

exports.renderAddEvent = (req, res) => {
  res.json({  title: 'إضافة مناسبة جديد' });
};

exports.addEvent = async (req, res) => {
  const { name, date, location } = req.body;
  try {
    const event = new Event({ name, date, location, manager: req.user.id });
    await event.save();
    res.status(201).json({
      message: 'تم إضافة المناسبة بنجاح',
      event
    });
  } catch (err) {
    res.json({ error: 'فشل إضافة المناسبة' });
  }
};

exports.renderEditEvent = async (req, res) => {
  try {
    const event = await Event.findOne({ _id: req.params.id, manager: req.user.id });
    if (!event) return res.status(404).send('المناسبة غير موجودة');
    res.json({ event });
  } catch (err) {
    res.status(500).send('خطأ في تحميل بيانات المناسبة');
  }
};

exports.editEvent = async (req, res) => {
  const { name, date, location } = req.body;
  try {
    const event = await Event.findOne({ _id: req.params.id, manager: req.user.id });
    if (!event) return res.status(404).send('المناسبة غير موجودة');
    event.name = name || event.name;
    event.date = date || event.date;
    event.location = location || event.location;
    await event.save();
    res.json({ message: 'تم تعديل المناسبة بنجاح', event });
  } catch (err) {
    res.status(500).send('فشل تعديل بيانات المناسبة');
  }
};

exports.deleteEvent = async (req, res) => {
  try {
    const event = await Event.findOneAndDelete({ _id: req.params.id, manager: req.user.id });
    if (!event) return res.status(404).send('المناسبة غير موجودة');
    res.json({ message: 'تم حذف المناسبة بنجاح' });
  } catch (err) {
    res.status(500).send('فشل حذف المناسبة');
  }
};