const Template = require('../models/Template');
const Hall = require('../models/Hall');
const path = require('path');
const fs = require('fs');

// عرض قائمة القوالب
exports.listTemplates = async (req, res) => {
  try {
    const templates = await Template.find().populate('hallId');
    res.json({ templates });
  } catch (err) {
    res.status(500).send('خطأ في تحميل القوالب');
  }
};

// عرض صفحة إضافة قالب
exports.renderAddTemplate = async (req, res) => {
  const halls = await Hall.find();
  res.json({ halls });
};

// إضافة قالب
exports.addTemplate = async (req, res) => {
  const { hallId, templateName, customizableFields } = req.body;
  const image = req.file;

  if (!image) return res.status(400).send('يجب رفع صورة للقالب');

  try {
    const newTemplate = new Template({
      hallId,
      templateName,
      imageUrl: '/uploads/' + image.filename,
      customizableFields: customizableFields ? customizableFields.split(',') : []
    });

    await newTemplate.save();
    res.json({ message: 'تم إضافة القالب بنجاح',newTemplate });
  } catch (err) {
    res.status(500).send('فشل في إضافة القالب');
  }
};

// عرض صفحة تعديل القالب
exports.renderEditTemplate = async (req, res) => {
  try {
    const template = await Template.findById(req.params.id);
    const halls = await Hall.find();
    if (!template) return res.status(404).send('القالب غير موجود');
    res.json({ template, halls });
  } catch (err) {
    res.status(500).send('خطأ في تحميل القالب');
  }
};

// تعديل القالب
exports.editTemplate = async (req, res) => {
  const { hallId, templateName, customizableFields } = req.body;
  const image = req.file;

  try {
    const template = await Template.findById(req.params.id);
    if (!template) return res.status(404).send('القالب غير موجود');

    template.hallId = hallId;
    template.templateName = templateName;
    template.customizableFields = customizableFields ? customizableFields.split(',') : [];

    if (image) {
      // حذف الصورة القديمة (اختياري)
      if (template.imageUrl) {
        const oldPath = path.join(__dirname, '..', 'public', template.imageUrl);
        if (fs.existsSync(oldPath)) fs.unlinkSync(oldPath);
      }
      template.imageUrl = '/uploads/' + image.filename;
    }

    await template.save();
    res.json({ message: 'تم تعديل القالب بنجاح', template });
  } catch (err) {
    res.status(500).send('فشل تعديل القالب');
  }
};

exports.deleteTemplate = async (req, res) => {
  try {
    await Template.findByIdAndDelete(req.params.id);
    res.json({ message: 'تم حذف القالب بنجاح' });
  } catch (err) {
    console.error(err);
    res.status(500).send('فشل حذف القالب');
  }
};
