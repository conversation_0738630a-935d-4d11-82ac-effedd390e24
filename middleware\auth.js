const { verifyToken, extractToken } = require('../utils/jwt');
const Admin = require('../models/Admin');
const User = require('../models/User');
const Client = require('../models/Client');

const publicPaths = [
  '/auth/login',
  '/auth/logout',
  '/auth/register',
  '/assets',
  '/css',
  '/js',
  '/favicon.ico',
  '/public',
  '/uploads'
];

// تحقق هل المسار ضمن المسارات العامة
const isPublicPath = (path) => {
  return publicPaths.some(publicPath => path.startsWith(publicPath));
};

// ميدلوير عام يحمي كل المسارات الخاصة
const getUserByRole = async (role, id) => {
  switch (role) {
    case 'admin':
      return await Admin.findById(id);
    case 'manager':
    case 'scanner':
      return await User.findById(id);
    case 'client':
      return await Client.findById(id);
    default:
      return null;
  }
};

const requireAuth = async (req, res, next) => {
  if (isPublicPath(req.path)) return next();

  const token = extractToken(req);
  if (!token) return res.redirect('/auth/login');

  try {
    const decoded = verifyToken(token); // يحتوي على id و role
    const user = await getUserByRole(decoded.role, decoded.id);

    if (!user || user.isActive === false) {
      return res.redirect('/auth/login');
    }

    req.user = user;
    req.userRole = decoded.role;
    next();
  } catch (error) {
    return res.redirect('/auth/login');
  }
};

const auth = {
  // للمسارات التي تحتاج استجابة JSON (API)
  apiUser: async (req, res, next) => {
    const token = extractToken(req);
    if (!token) return res.status(401).json({ error: 'No token provided.' });

    try {
      const decoded = verifyToken(token);
      const user = await getUserByRole(decoded.role, decoded.id);
      if (!user || user.isActive === false) {
        return res.status(401).json({ error: 'Invalid or inactive user.' });
      }

      req.user = user;
      req.userRole = decoded.role;
      next();
    } catch (error) {
      return res.status(401).json({ error: 'Invalid token.' });
    }
  },

  // للمستخدمين من نوع معين (مثلاً العميل)
  role: (allowedRoles = []) => {
    return async (req, res, next) => {
      const token = extractToken(req);
      if (!token) return res.redirect('/auth/login');

      try {
        const decoded = verifyToken(token);
        if (!allowedRoles.includes(decoded.role)) {
          return res.redirect('/auth/login');
        }

        const user = await getUserByRole(decoded.role, decoded.id);
        if (!user || user.isActive === false) {
          return res.redirect('/auth/login');
        }

        req.user = user;
        req.userRole = decoded.role;
        next();
      } catch (error) {
        return res.redirect('/auth/login');
      }
    };
  }
};

module.exports = {
  requireAuth,
  auth,
  isPublicPath
};
