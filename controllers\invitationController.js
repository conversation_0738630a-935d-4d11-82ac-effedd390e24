const Invitation = require('../models/Invitation');
const QRCode = require('qrcode');

exports.createInvitation = async (req, res) => {
  const { guestName, numOfPeople, eventDate } = req.body;
  const clientId = req.user.id;

  try {
    const payload = `${guestName}|${numOfPeople}|${eventDate}|${clientId}`;
    const qrCode = await QRCode.toDataURL(payload);

    const invitation = new Invitation({
      clientId,
      guestName,
      numOfPeople,
      eventDate,
      qrCode
    });

    await invitation.save();
    res.status(201).json(invitation);
  } catch (err) {
    res.status(500).json({ message: 'Failed to create invitation', error: err });
  }
};

exports.getMyInvitations = async (req, res) => {
  try {
    const invitations = await Invitation.find({ clientId: req.user.id });
    res.json(invitations);
  } catch (err) {
    res.status(500).json({ message: 'Failed to fetch invitations', error: err });
  }
};
