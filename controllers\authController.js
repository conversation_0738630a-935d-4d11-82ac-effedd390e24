const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const Client = require('../models/Client');
const Admin = require('../models/Admin');
const { generateAuthTokens } = require('../utils/jwt');

exports.login = async (req, res) => {
  try {
    const { phone, password } = req.body;

    let user = null;
    let role = null;

    const admin = await Admin.findOne({ phone });
    if (admin) {
      const isMatch = await admin.comparePassword(password);
      if (!isMatch) return res.status(400).json({ error: 'كلمة المرور غير صحيحة' });
      if (!admin.isActive) return res.status(400).json({ error: 'تم إلغاء تفعيل حسابك' });

      user = admin;
      role = 'admin';
    } else {
      const managerOrScanner = await User.findOne({ phone });
      if (managerOrScanner) {
        const isMatch = await managerOrScanner.comparePassword(password);
        if (!isMatch) return res.status(400).json({ error: 'كلمة المرور غير صحيحة' });
        if (!managerOrScanner.isActive) return res.status(400).json({ error: 'تم إلغاء تفعيل حسابك' });

        user = managerOrScanner;
        role = managerOrScanner.role;
      } else {
        const client = await Client.findOne({ phone });
        if (client) {
          const isMatch = await client.comparePassword(password);
          if (!isMatch) return res.status(400).json({ error: 'كلمة المرور غير صحيحة' });
          if (!client.isActive) return res.status(400).json({ error: 'تم إلغاء تفعيل حسابك' });

          user = client;
          role = 'client';
        }
      }
    }

    if (!user) return res.status(400).json({ error: 'رقم الهاتف غير مسجل' });

    // إنشاء التوكنات
    const tokens = generateAuthTokens(user, role);

    // إرسال الكوكيز
    res.cookie('token', tokens.accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 24 * 60 * 60 * 1000,
      sameSite: 'strict'
    });

    res.cookie('refreshToken', tokens.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 7 * 24 * 60 * 60 * 1000,
      sameSite: 'strict'
    });

    // بيانات المستخدم للرد (دون كلمات المرور أو معلومات حساسة)
    const safeUser = {
      _id: user._id,
      phone: user.phone,
      role
    };

    res.json({ tokens, user: safeUser, role });

  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'حدث خطأ أثناء تسجيل الدخول' });
  }
};


// تسجيل مستخدم جديد
exports.register = async (req, res) => {
  try {
    const { name, phone, password, role } = req.body;

    // التحقق من وجود المستخدم
    const existingUser = await User.findOne({ phone });
    if (existingUser) {
      return res.status(400).json({ 
        title: 'إنشاء حساب', 
        error: 'البريد الإلكتروني مستخدم بالفعل' 
      });
    }

    // تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash(password, 12);

    // إنشاء المستخدم
    const user = new User({
      name,
      phone,
      password: hashedPassword,
      role: role || 'client'
    });

    await user.save();

    res.status(201).json({ 
      message: 'تم إنشاء الحساب بنجاح',
      user: {
        _id: user._id,
        name: user.name,
        phone: user.phone,
        role: user.role
      }
    });
  } catch (error) {
    console.error('Register error:', error);
    res.status(500).json({ 
      title: 'إنشاء حساب', 
      error: 'حدث خطأ في السيرفر' 
    });
  }
};

// تسجيل الخروج
exports.logout = (req, res) => {
    // مسح الـ cookies
    res.clearCookie('token');
    res.clearCookie('refreshToken');

    res.json({ message: 'تم تسجيل الخروج بنجاح' });
};
