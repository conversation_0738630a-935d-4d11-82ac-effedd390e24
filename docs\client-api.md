## Client Routes (/client) - يتطلب role: client

### GET /client/dashboard
**الوصف:** لوحة تحكم العميل
**الاستجابة:**
```json
{
  "user": {
    "name": "string",
    "phone": "string"
  },
  "events": [
    {
      "id": "ObjectId",
      "eventName": "string", 
      "eventDate": "date",
      "status": "string",
      "hall": {
        "name": "string",
        "location": "string"
      }
    }
  ]
}
```

### GET /client/events/:id
**URL Parameters:** id (ObjectId)
**الوصف:** تفاصيل الفعالية

### GET /client/events/:id/invitations
**URL Parameters:** id (ObjectId)
**الوصف:** دعوات الفعالية

### POST /client/events/:id/invitations
**URL Parameters:** id (ObjectId)
**المعاملات المرسلة:**
```json
{
  "guests": [
    {
      "name": "string (required)",
      "phone": "string (optional)"
    }
  ]
}
```

### GET /client/profile
**الوصف:** الملف الشخصي

### POST /client/profile
**المعاملات المرسلة:**
```json
{
  "name": "string",
  "phone": "string",
  "currentPassword": "string (required for password change)",
  "newPassword": "string (optional)"
}
```