const User = require('../models/User');
const Client = require('../models/Client')
const Hall = require('../models/Hall');
const bcrypt = require('bcrypt');



// لوحة تحكم المدير
exports.dashboard = (req, res) => {
  res.json({ 
    title: 'لوحة تحكم المدير',
    user: req.user 
  });
};

exports.listClients = async (req, res) => {
  try {
    const clients = await Client.find({ createdBy: req.user.id });
    res.json({ clients });
  } catch (err) {
    res.status(500).send('خطأ في تحميل العملاء');
  }
};

exports.renderAddClient = (req, res) => {
  res.json({ title: 'إضافة عميل جديد' });
};

exports.addClient = async (req, res) => {
  const { name, phone, password } = req.body;
  try {
    const existing = await Client.findOne({ phone });
    if (existing) return res.json({ error: 'رقم الهاتف مستخدم بالفعل' });
    const client = new Client({ name, phone, password, createdBy: req.user.id });
    await client.save();
    res.status(201).json({ message: 'تم إضافة العميل بنجاح', client: {
      _id: client._id,
      name: client.name,
      phone: client.phone
    }});
  } catch (err) {
    res.status(500).json({ error: 'فشل إضافة العميل' });
  }
};

exports.renderEditClient = async (req, res) => {
  try {
    const client = await Client.findOne({ _id: req.params.id, createdBy: req.user.id });
    if (!client) return res.status(404).send('العميل غير موجود');
    res.json({ client });
  } catch (err) {
    res.status(500).send('خطأ في تحميل بيانات العميل');
  }
};

exports.editClient = async (req, res) => {
  const { name, phone, password, role } = req.body;
  try {
    const client = await Client.findOne({ _id: req.params.id, createdBy: req.user.id });
    if (!client) return res.status(404).send('العميل غير موجود');
    client.name = name || client.name;
    client.phone = phone || client.phone;
    if (password) client.password = await bcrypt.hash(password, 10);
    await client.save();
    res.status(201).json({ message: 'تم تعديل بيانات العميل بنجاح', client: {
      _id: client._id,
      name: client.name,
      phone: client.phone
    }});
  } catch (err) {
    res.status(500).send('فشل تعديل بيانات العميل');
  }
};

exports.deleteClient = async (req, res) => {
  try {
    const client = await Client.findOneAndDelete({ _id: req.params.id, createdBy: req.user.id });
    if (!client) return res.status(404).send('العميل غير موجود');
    res.status(201).json({ message: 'تم حذف العميل بنجاح'});
  } catch (err) {
    res.status(500).send('فشل حذف العميل');
  }
};

exports.deleteManager = async (req, res) => {
  try {
    const manager = await User.findOneAndDelete({ _id: req.params.id, role: 'manager' });
    if (!manager) return res.status(404).send('المدير غير موجود');
    res.status(201).json({ message: 'تم حذف المدير بنجاح'});
  } catch (err) {
    res.status(500).send('فشل حذف المدير');
  }
}; 



//hall
exports.getHalls = async (req, res) => {
  try {
    const halls = await Hall.find().populate('generalManager', 'name phone');
    res.json(halls);
  } catch (err) {
    res.status(500).json({ message: 'Failed to fetch halls', error: err });
  }
};

exports.renderAddHall = (req, res) => {
  res.json({ title: 'إضافة صالة جديدة' });
};

exports.createHall = async (req, res) => {
  const { name, location, tables, chairs, defaultPrices, managerName, managerPhone, managerPassword } = req.body;

  try {
    const existingManager = await User.findOne({ phone: managerPhone });
    if (existingManager) return res.status(400).json({ message: 'Manager phone already exists' });

    const newManager = new User({ name: managerName, phone: managerPhone, password: managerPassword, role: 'manager' });
    await newManager.save();

    const newHall = new Hall({
      name,
      location,
      tables,
      chairs,
      defaultPrices,
      generalManager: newManager._id
    });

    await newHall.save();
     // ✅ الآن حدث المستخدم وأضف hallId
    newManager.hallId = newHall._id;
    await newManager.save();
    res.status(201).json({ message: 'Hall and manager created successfully' });
  } catch (err) {
    res.status(500).json({ message: 'Failed to create hall', error: err });
  }
};

exports.renderEditHall = async (req, res) => {
  try {
    const hall = await Hall.findById(req.params.id).populate('generalManager', 'name phone');
    if (!hall) return res.status(404).send('Hall not found');
    res.json({ hall });
  } catch (err) {
    res.status(500).send('خطأ في تحميل بيانات الصالة');
  }
};


exports.editHall = async (req, res) => {
  const { id } = req.params;
  const { name, location, tables, chairs, defaultPrices } = req.body;
  try {
    const hall = await Hall.findById(id);
    if (!hall) return res.status(404).json({ message: 'Hall not found' });
    hall.name = name || hall.name;
    hall.location = location || hall.location;
    hall.tables = tables || hall.tables;
    hall.chairs = chairs || hall.chairs;
    hall.defaultPrices = defaultPrices || hall.defaultPrices;
    await hall.save();
    res.json({ message: 'Hall updated successfully', hall });
  } catch (err) {
    res.status(500).json({ message: 'Failed to update hall', error: err });
  }
};

exports.deleteHall = async (req, res) => {
  const { id } = req.params;
  try {
    const hall = await Hall.findOneAndDelete(id);
    if (!hall) return res.status(404).send('الصالة غير موجود');
    res.json({ message: 'Hall delete successfully'});
  } catch (err) {
    res.status(500).json({ message: 'Failed to update hall', error: err });
  }
};

exports.renderHallsList = async (req, res) => {
  try {
    const halls = await Hall.find().populate('generalManager', 'name phone');
    res.json({ halls });
  } catch (err) {
    res.status(500).send('خطأ في تحميل الصالات');
  }
};