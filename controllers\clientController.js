// controllers/hallController.js
const User = require('../models/User');
const Hall = require('../models/Hall');
const Event = require('../models/Event');
const Template = require('../models/Template');
const Complaint = require('../models/Complaint');
const Invitation = require('../models/Invitation')
const QRCode = require('qrcode');
const bcrypt = require('bcrypt');
const { createCanvas, loadImage } = require('canvas');
const path = require('path');

// لوحة تحكم مدير الصالة
exports.renderDashboard = async (req, res) => {
  try {
    const clientId = req.user._id;

    // جلب الحفلة الخاصة بالعميل
    const event = await Event.findOne({ clientId }).populate('hallId');

    if (!event) {
      return res.json({
        title: 'لوحة تحكم العميل',
        event: null,
        invitationsCount: 0
      });
    }

    // عدّ عدد الدعوات المرتبطة بالحفلة
    const invitationsCount = await Invitation.countDocuments({ eventId: event._id });

    res.json({
      title: 'لوحة تحكم العميل',
      event,
      invitationsCount
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'حدث خطأ في تحميل البيانات', error: err });
  }
};

exports.listInvitations = async (req, res) => {
  try {
    const clientId = req.user._id;
    const invitations = await Invitation.find({clientId})
      .populate('eventId')  // لجلب بيانات المناسبة المرتبطة
      .sort({ createdAt: -1 });

    res.json({ invitations ,error : null, title : 'عرض الدعوات' });
  } catch (err) {
    console.error(err);
    res.json({ invitations: [], title : 'عرض الدعوات' , error: 'حدث خطأ أثناء تحميل الدعوات' });
  }
};

exports.getInvitationDetails = async (req, res) => {
  try {
    const invitation = await Invitation.findById(req.params.id).populate('eventId');
    if (!invitation)
    {
      return res.status(404).json({ error: 'الدعوة غير موجودة' });
    }

    res.json({ invitation ,title : 'عرض الدعوة'});
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'حدث خطأ أثناء جلب بيانات الدعوة' });
  }
};


exports.renderAddInvitations = async (req, res) => {
  try {
    const event = await Event.findOne({ clientId: req.user._id });
    if (!event) return res.status(404).send('الحفلة غير موجودة');
    res.json({ title: 'إضافة دعوة', event, error: null });
  } catch (err) {
    console.error(err);
    res.json({ error: 'حدث خطأ في تحميل المناسبات', events: [] });
  }
};

exports.AddInvitations = async (req, res) => {
  try {
    const clientId = req.user._id;
    const { guestName, numOfPeople } = req.body;

    const event = await Event.findOne({ clientId }).populate('templateId').populate('hallId');
    const eventId = event._id;

    if (!guestName || !numOfPeople) {
      return res.json({ error: 'جميع الحقول مطلوبة', event });
    }

    const aggregate = await Invitation.aggregate([
      { $match: { eventId: event._id } },
      { $group: { _id: null, totalPeople: { $sum: "$numOfPeople" } } }
    ]);
    const totalPeopleSoFar = aggregate.length > 0 ? aggregate[0].totalPeople : 0;

    if (totalPeopleSoFar + Number(numOfPeople) > event.numOfPeople) {
      return res.json({ error: 'تجاوز عدد الأشخاص المسموح به للمناسبة', event });
    }

    // إنشاء كود QR
    const qrCodeValue = `${guestName.replace(/\s/g, '')}${numOfPeople}${Date.now().toString().slice(-4)}`;
    const qrCodeDataURL = await QRCode.toDataURL(qrCodeValue);
    // تحميل صورة القالب
    const imagePath = path.join(__dirname, '..', 'public', event.templateId.imageUrl); // ← مسار فعلي للصورة
    const templateImage = await loadImage(imagePath);
    const canvas = createCanvas(templateImage.width, templateImage.height);
    const ctx = canvas.getContext('2d');

    // رسم القالب
    ctx.drawImage(templateImage, 0, 0);
    // كتابة اسم الضيف وعدد الأشخاص
    ctx.font = '24px Arial';
    ctx.fillStyle = '#e0c946ff';
    ctx.direction = 'rtl'; // ضروري للعربية
    ctx.textAlign = 'center';
    if(event.statusWrite === true){
      ctx.fillText(`الى السيد : ${guestName} ندعوكم لحضور ${event.eventName}`, templateImage.width / 2, templateImage.height / 2);
      ctx.fillText(`الدعوة مخصصة ل : ${numOfPeople} شخص`, templateImage.width / 2, templateImage.height / 2 + 50);
    }
    ctx.fillText(`وذلك بتاريخ: ${event.eventDate.toISOString().slice(0,10)}`, templateImage.width / 2,  templateImage.height / 2 + 100);
    ctx.fillText(`العنوان: ${event.hallId.location}`, templateImage.width / 2, templateImage.height / 2 + 150);

    // رسم QR Code داخل القالب
    const qrImage = await loadImage(qrCodeDataURL);
    ctx.drawImage(qrImage, canvas.width / 2 - 60, canvas.height / 4, 120, 120);


    // تحويل الناتج إلى صورة base64
    const finalImage = canvas.toDataURL();

    // حفظ الدعوة
    const invitation = new Invitation({
      clientId,
      guestName,
      numOfPeople,
      eventDate: event.eventDate,
      qrCode: qrCodeValue,
      qrCodeImage: finalImage,
      used: false,
      eventId
    });

    await invitation.save();
    res.status(201).json({
      message: 'تم إضافة الدعوة بنجاح',
      invitation: {
        _id: invitation._id,
        guestName: invitation.guestName,
        numOfPeople: invitation.numOfPeople,
        eventDate: invitation.eventDate,
        qrCodeImage: invitation.qrCodeImage,
        used: invitation.used,
        eventId: invitation.eventId
      }
    });

  } catch (err) {
    console.error(err);
    const event = await Event.findOne({ clientId: req.user._id });
    res.json({ error: 'حدث خطأ أثناء الحفظ', event });
  }
};

exports.renderEditInvitations = async (req, res) => {
  try {
    const invitation = await Invitation.findOne({
      _id: req.params.id,
      clientId: req.user._id
    }).populate('eventId');

    if (!invitation) return res.status(404).send('الدعوة غير موجودة');

    res.json( {
      title: 'تعديل الدعوة',
      invitation,
      error : null
    });
  } catch (err) {
    res.status(500).send('خطأ في الخادم');
  }
};

exports.EditInvitations = async (req, res) => {
  try {
    const { guestName, numOfPeople } = req.body;
    const invitationId = req.params.id;
    const clientId = req.user._id;

    // التأكد من وجود الدعوة
    const invitation = await Invitation.findById(invitationId);
    if (!invitation) return res.status(404).json({ error: 'الدعوة غير موجودة' });

    // جلب المناسبة الخاصة بالعميل
    const event = await Event.findOne({ clientId });
    if (!event) {
      return res.json( {
        error: 'المناسبة غير موجودة',
        title: 'تعديل الدعوة',
        event: null,
        invitation
      });
    }

    // التأكد من عدم تجاوز الحد المسموح
    const aggregate = await Invitation.aggregate([
      { $match: { eventId: event._id, _id: { $ne: invitation._id } } },
      { $group: { _id: null, totalPeople: { $sum: "$numOfPeople" } } }
    ]);
    const totalPeopleSoFar = aggregate.length > 0 ? aggregate[0].totalPeople : 0;

    if (totalPeopleSoFar + Number(numOfPeople) > event.numOfPeople) {
      // تحديث القيم للعرض فقط
      invitation.guestName = guestName;
      invitation.numOfPeople = numOfPeople;

      return res.json({
        error: 'تجاوز عدد الأشخاص المسموح به للمناسبة',
        title: 'تعديل الدعوة',
        event,
        invitation
      });
    }
    // إعادة إنشاء كود QR إن لم يكن موجودًا أو إذا تغيّر عدد الأشخاص
    const isQrMissingOrChanged = !invitation.qrCode || invitation.numOfPeople !== Number(numOfPeople);
    if (isQrMissingOrChanged) {
      const clientName = req.user.name || "CLT";
      const eventName = event.name || "EVT";
      const clientPart = clientName.replace(/\s+/g, '').substring(0, 3).toUpperCase();
      const eventPart = eventName.replace(/\s+/g, '').substring(0, 3).toUpperCase();
      const randomPart = Math.floor(100 + Math.random() * 900);
      const qrCodeValue = `${clientPart}${numOfPeople}${eventPart}${randomPart}`;
      const qrCodeImage = await QRCode.toDataURL(qrCodeValue);

      invitation.qrCode = qrCodeValue;
      invitation.qrCodeImage = qrCodeImage;
    }
    // إنشاء QR في حال لم يكن موجود
   /* if (!invitation.qrCode) {
      const qrCodeValue = Math.random().toString(36).substring(2, 10).toUpperCase();
      invitation.qrCode = await QRCode.toDataURL(qrCodeValue);
    }*/

    // تحديث بيانات الدعوة
    invitation.guestName = guestName;
    invitation.numOfPeople = numOfPeople;
    invitation.eventId = event._id;
    invitation.eventDate = event.eventDate;

    await invitation.save();

    res.json({
      message: 'تم تعديل الدعوة بنجاح',
      invitation: {
        _id: invitation._id,
        guestName: invitation.guestName,
        numOfPeople: invitation.numOfPeople,
        eventDate: invitation.eventDate,
        qrCodeImage: invitation.qrCodeImage,
        used: invitation.used,
        eventId: invitation.eventId
      }
    });
  } catch (err) {
    console.error(err);

    const invitation = await Invitation.findById(req.params.id);
    const event = await Event.findOne({ clientId: req.user._id });

    res.json({
      error: 'حدث خطأ أثناء التعديل',
      title: 'تعديل الدعوة',
      event,
      invitation
    });
  }
};

exports.DeleteInvitations = async (req, res) => {
  try {
    const invitation = await Invitation.findOneAndDelete({ _id: req.params.id});
    if (!invitation) return res.status(404).send('الدعوة غير موجودة');
    res.json({ message: 'تم حذف الدعوة بنجاح' });
  } catch (err) {
    res.status(500).send('فشل حذف الدعوة');
  }
};