
const mongoose = require('mongoose');

const eventSchema = new mongoose.Schema({
  hallId: { type: mongoose.Schema.Types.ObjectId, ref: 'Hall', required: true },
  eventName: { type: String, required: true },
  eventType: { type: String, required: true },
  eventDate: { type: Date, required: true },
  startTime: { type: String, required: true }, // مثال: "18:00"
  endTime: { type: String, required: true },   // مثال: "22:00"
  clientId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  templateId: { type: mongoose.Schema.Types.ObjectId, ref: 'Template' },
  statusWrite: { type: Boolean ,default : true},
  status: { type: String, enum: ['scheduled','confirmed', 'cancelled','completed'], default: 'scheduled' },
  numOfPeople: { type: Number, required: true },  // عدد الأشخاص المسموح به
}, { timestamps: true });

module.exports = mongoose.model('Event', eventSchema);

