const Invitation = require('../models/Invitation');
const Event = require('../models/Event');
const Hall = require('../models/Hall');
const User = require('../models/User')

exports.getStatisticsAdmin = async (req, res) => {
  try {
    const totalHalls = await Hall.countDocuments();
    const totalUsers = await User.countDocuments();
    const totalEvents = await Event.countDocuments();

    res.json({
      totalHalls,
      totalUsers,
      totalEvents
    });
  } catch (err) {
    res.status(500).json({ message: 'Failed to generate statistics', error: err });
  }
};

exports.getStatistics = async (req, res) => {
  const { start, end } = req.query;
  const startDate = start ? new Date(start) : new Date('2000-01-01');
  const endDate = end ? new Date(end) : new Date();

  try {
    const totalInvitations = await Invitation.countDocuments({ createdAt: { $gte: startDate, $lte: endDate } });
    const usedInvitations = await Invitation.countDocuments({ used: true, createdAt: { $gte: startDate, $lte: endDate } });
    const totalEvents = await Event.countDocuments({ eventDate: { $gte: startDate, $lte: endDate } });

    res.json({
      totalInvitations,
      usedInvitations,
      totalEvents,
      attendanceRate: totalInvitations > 0 ? ((usedInvitations / totalInvitations) * 100).toFixed(2) + '%' : '0%'
    });
  } catch (err) {
    res.status(500).json({ message: 'Failed to generate statistics', error: err });
  }
};