
const mongoose = require('mongoose');

const complaintSchema = new mongoose.Schema({
  fromUserId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  hallId:{type: mongoose.Schema.Types.ObjectId, ref: 'Hall', required: true},
  message: { type: String, required: true },
  reply: { type: String },
  status: { type: String, enum: ['open', 'closed'], default: 'open' }
}, { timestamps: true });

module.exports = mongoose.model('Complaint', complaintSchema);