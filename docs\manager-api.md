## Manager Routes (/manager) - يتطلب role: manager

### GET /manager/dashboard
**الوصف:** لوحة تحكم المدير
**الاستجابة:**
```json
{
  "hall": {
    "id": "ObjectId",
    "name": "string",
    "location": "string"
  },
  "todayEvents": "array",
  "upcomingEvents": "array",
  "stats": {
    "totalEvents": "number",
    "totalClients": "number"
  }
}
```

### GET /manager/hall
**الوصف:** معلومات القاعة

### POST /manager/halls
**الوصف:** تحديث معلومات القاعة
**المعاملات المرسلة:**
```json
{
  "name": "string",
  "location": "string", 
  "tables": "number",
  "chairs": "number",
  "defaultPrices": {
    "morning": "number",
    "evening": "number"
  }
}
```

### GET /manager/hall/events
**الوصف:** صفحة إدارة الفعاليات

### POST /manager/hall/events
**الوصف:** إضافة فعالية جديدة
**المعاملات المرسلة:**
```json
{
  "eventName": "string (required)",
  "eventDate": "date (required)",
  "startTime": "string (required)", 
  "endTime": "string (required)",
  "eventType": "string",
  "chairsCount": "number (required)",
  "clientName": "string (required)",
  "phone": "string (required)",
  "password": "string (required)",
  "templateId": "ObjectId",
  "statusWrite": "boolean"
}
```

### GET /manager/events/:id/edit
**URL Parameters:** id (ObjectId)

### PUT /manager/events/:id
**URL Parameters:** id (ObjectId)
**المعاملات المرسلة:**
```json
{
  "eventName": "string",
  "eventDate": "date",
  "startTime": "string",
  "endTime": "string", 
  "numOfPeople": "number",
  "templateId": "ObjectId"
}
```

### DELETE /manager/events/:id
**URL Parameters:** id (ObjectId)

### GET /manager/clients
**الوصف:** قائمة عملاء القاعة

### GET /manager/staff
**الوصف:** قائمة الموظفين

### POST /manager/staff/add
**المعاملات المرسلة:**
```json
{
  "name": "string (required)",
  "phone": "string (required)",
  "password": "string (required)",
  "role": "scanner"
}
```

### GET /manager/staff/edit/:id
**URL Parameters:** id (ObjectId)

### POST /manager/staff/edit/:id
**URL Parameters:** id (ObjectId)

### GET /manager/staff/delete/:id
**URL Parameters:** id (ObjectId)

### GET /manager/templates
**الوصف:** قوالب القاعة

### POST /manager/templates/add
**Content-Type:** multipart/form-data
**المعاملات المرسلة:**
```json
{
  "name": "string (required)",
  "description": "string",
  "image": "file (required)"
}
```

### GET /manager/complaints
**الوصف:** شكاوى القاعة