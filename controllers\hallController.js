// controllers/hallController.js
const User = require('../models/User');
const Hall = require('../models/Hall');
const Event = require('../models/Event');
const Template = require('../models/Template');
const Complaint = require('../models/Complaint');
const bcrypt = require('bcrypt');

// لوحة تحكم مدير الصالة
exports.renderDashboard = async (req, res) => {
  try {
    const hallId = req.user.hallId;
    const clients = await Client.countDocuments({ hallId});
    const events = await Event.countDocuments({ hallId });
    const staff = await User.countDocuments({ hallId, role: { $in: ['supervisor', 'scanner'] } });
    res.json({ title: 'لوحة التحكم', stats: { clients, events, staff } });
  } catch (err) {
    res.status(500).send('خطأ في تحميل لوحة التحكم');
  }
};

// --- إدارة الصالة ---
exports.renderManageHall = async (req, res) => {
  try {
    const hall = await Hall.findById(req.user.hallId);
    res.json({ title: 'إدارة الصالة', hall });
  } catch (err) {
    res.status(500).send('فشل تحميل بيانات الصالة');
  }
};

exports.updateHall = async (req, res) => {
  try {
    const { name, chairs, tables, basePrice } = req.body;
    await Hall.findByIdAndUpdate(req.user.hallId, {
      name,
      chairs,
      tables,
      basePrice
    });
    res.json({
      message: 'تم تعديل بيانات الصالة بنجاح',
      hall: updatedHall
    });
  } catch (err) {
    res.status(500).send('فشل تعديل بيانات الصالة');
  }
};

// --- إدارة المناسبات (الحفلات) ---
exports.listEvents = async (req, res) => {
  try {
    const events = await Event.find({ hallId: req.user.hallId }).populate('clientId').lean();
    res.json({ title: 'إدارة الحفلات', events });
  } catch (err) {
    res.status(500).send('فشل تحميل الحفلات');
  }
};

exports.renderAddEvent = (req, res) => {
  res.json({ title: 'إضافة مناسبة' });
};

exports.addEvent = async (req, res) => {
  try {
    const { eventName, type, date, clientId, chairsAllocated } = req.body;
    const exists = await Event.findOne({ date, hallId: req.user.hallId });
    if (exists) return res.json({ error: 'تاريخ محجوز مسبقاً' });
    const event = await Event.create({ eventName, type, date, clientId, chairsAllocated, hallId: req.user.hallId });
    res.status(201).json({
      message: 'تم إضافة المناسبة بنجاح',
      event
    });
  } catch (err) {
    res.status(500).send('فشل إضافة المناسبة');
  }
};

exports.renderEditEvent = async (req, res) => {
  try {
    const event = await Event.findById(req.params.id);
    if (!event || event.hallId.toString() !== req.user.hallId.toString()) {
      return res.json({ error: 'الحفلة غير موجودة' });
    }
    res.json({ title: 'تعديل المناسبة', event });
  } catch (err) {
    console.error(err);
    res.json({ error: 'حدث خطأ أثناء تحميل الحفلة' });
  }
};

exports.editEvent = async (req, res) => {
  try {
    const { eventName, type, date, clientId, chairsAllocated } = req.body;
    const eventId = req.params.id;

    const event = await Event.findById(eventId);
    if (!event || event.hallId.toString() !== req.user.hallId.toString()) {
      return res.json({ error: 'الحفلة غير موجودة' });
    }

    // تحقق من وجود مناسبة أخرى بنفس التاريخ في نفس القاعة
    const exists = await Event.findOne({
      _id: { $ne: eventId },
      date,
      hallId: req.user.hallId
    });

    if (exists) {
      return res.json({
        title: 'تعديل المناسبة',
        event: { ...event.toObject(), eventName, type, date, clientId, chairsAllocated },
        error: 'تاريخ محجوز مسبقاً'
      });
    }

    // تحديث الحقول
    event.eventName = eventName;
    event.type = type;
    event.date = date;
    event.clientId = clientId;
    event.chairsAllocated = chairsAllocated;

    await event.save();

    res.json({
      message: 'تم تعديل المناسبة بنجاح',
      event
    });
  } catch (err) {
    console.error(err);
    res.status(500).send('فشل تعديل المناسبة');
  }
};

exports.deleteEvent = async (req, res) => {
  try {
    const event = await Event.findById(req.params.id);

    // التأكد من أن الحدث موجود ويتبع للقاعة الحالية
    if (!event || event.hallId.toString() !== req.user.hallId.toString()) {
      return res.json({ error: 'الحفلة غير موجودة' });
    }

    await Event.deleteOne({ _id: req.params.id });

    res.json({ message: 'تم حذف المناسبة بنجاح' });
  } catch (err) {
    console.error(err);
    res.status(500).send('فشل حذف المناسبة');
  }
};


// --- إدارة العملاء ---
exports.listClients = async (req, res) => {
  try {
    const clients = await Client.find({ hallId: req.user.hallId });
    res.json({ title: 'إدارة العملاء', clients });
  } catch (err) {
    res.status(500).send('فشل تحميل العملاء');
  }
};

// عرض صفحة إضافة عميل
exports.renderAddClient = (req, res) => {
  res.json({ title: 'إضافة عميل' });
};

// إضافة عميل جديد
exports.addClient = async (req, res) => {
  try {
    const { name, phone, password } = req.body;

    // تحقق من عدم تكرار الإيميل
    const exists = await Client.findOne({ phone });
    if (exists) {
      return res.json({ error: 'البريد الإلكتروني مستخدم مسبقًا', title: 'إضافة عميل' });
    }

    const client = await Client.create({
      name,
      phone,
      password,
      hallId: req.user.hallId // ربط العميل بالقاعة
    });

    res.json({ message: 'تم إضافة العميل بنجاح', client });
  } catch (err) {
    console.error(err);
    res.status(500).send('فشل في إضافة العميل');
  }
};

// عرض صفحة تعديل عميل
exports.renderEditClient = async (req, res) => {
  try {
    const client = await Client.findById(req.params.id);

    if (!client || client.hallId.toString() !== req.user.hallId.toString()) {
      return res.json({ error: 'العميل غير موجود' });
    }

    res.json({ title: 'تعديل عميل', client });
  } catch (err) {
    console.error(err);
    res.json({ error: 'حدث خطأ أثناء تحميل العميل' });
  }
};

// تعديل بيانات عميل
exports.editClient = async (req, res) => {
  try {
    const { name, phone } = req.body;
    const client = await Client.findById(req.params.id);

    if (!client || client.hallId.toString() !== req.user.hallId.toString()) {
      return res.json({ error: 'العميل غير موجود' });
    }

    // تحقق من عدم تكرار الإيميل لعميل آخر
    const existing = await Client.findOne({ phone, _id: { $ne: client._id } });
    if (existing) {
      return res.json( {
        error: 'رقم الهاتف مستخدم من قبل عميل آخر',
        title: 'تعديل عميل',
        client: { ...client.toObject(), name, phone }
      });
    }

    client.name = name;
    client.phone = phone;
    client.password = password;

    await Client.save();

    res.json({ message: 'تم تعديل بيانات العميل بنجاح', client });
  } catch (err) {
    console.error(err);
    res.status(500).send('فشل في تعديل بيانات العميل');
  }
};

// حذف عميل
exports.deleteClient = async (req, res) => {
  try {
    const client = await Client.findById(req.params.id);

    if (!client || client.hallId.toString() !== req.user.hallId.toString()) {
      return res.json({ error: 'العميل غير موجود' });
    }

    await Client.deleteOne({ _id: client._id });

    res.json({ message: 'تم حذف العميل بنجاح' });
  } catch (err) {
    console.error(err);
    res.status(500).send('فشل في حذف العميل');
  }
};


// --- إدارة الموظفين ---
exports.listStaff = async (req, res) => {
  try {
    const staff = await User.find({ hallId: req.user.hallId, role: { $in: ['supervisor', 'scanner'] } });
    res.json({ title: 'إدارة الموظفين', staff });
  } catch (err) {
    res.status(500).send('فشل تحميل الموظفين');
  }
};

// عرض صفحة إضافة موظف
exports.renderAddStaff = (req, res) => {
  res.json({ title: 'إضافة موظف' });
};

// إضافة موظف جديد
exports.addStaff = async (req, res) => {
  try {
    const { name, phone, password, role } = req.body;

    // تحقق من عدم وجود إيميل مكرر
    const exists = await User.findOne({ phone });
    if (exists) {
      return res.json({ error: 'البريد الإلكتروني مستخدم مسبقًا', title: 'إضافة موظف' });
    }

    // إنشاء الموظف مع حفظ hallId من المستخدم الحالي (مثلاً المدير)
    const staff = await User.create({
      name,
      phone,
      password,  // يمكنك تشفيره هنا أو في الموديل
      role,
      hallId: req.user.hallId
    });

    res.json({ message: 'تم إضافة الموظف بنجاح', staff });
  } catch (err) {
    console.error(err);
    res.status(500).send('فشل إضافة الموظف');
  }
};

// عرض صفحة تعديل موظف
exports.renderEditStaff = async (req, res) => {
  try {
    const staff = await User.findById(req.params.id);

    if (!staff || staff.hallId.toString() !== req.user.hallId.toString()) {
      return res.json({ error: 'الموظف غير موجود' });
    }

    res.json({ title: 'تعديل موظف', staff });
  } catch (err) {
    console.error(err);
    res.json({ error: 'حدث خطأ أثناء تحميل الموظف' });
  }
};

// تعديل بيانات الموظف
exports.editStaff = async (req, res) => {
  try {
    const { name, phone, password, role } = req.body;
    const staff = await User.findById(req.params.id);

    if (!staff || staff.hallId.toString() !== req.user.hallId.toString()) {
      return res.json({ error: 'الموظف غير موجود' });
    }

    // تحقق من عدم تكرار الإيميل
    const existing = await Staff.findOne({ phone, _id: { $ne: staff._id } });
    if (existing) {
      return res.json({
        error: 'البريد الإلكتروني مستخدم من قبل موظف آخر',
        title: 'تعديل موظف',
        staff: { ...staff.toObject(), name, phone, role }
      });
    }

    staff.name = name;
    staff.phone = phone;
    if (password && password.trim() !== '') {
      staff.password = password; // فكّر بتشفيرها
    }
    staff.role = role;

    await User.save();

    res.json({ message: 'تم تعديل بيانات الموظف بنجاح', staff });
  } catch (err) {
    console.error(err);
    res.status(500).send('فشل تعديل بيانات الموظف');
  }
};

// حذف موظف
exports.deleteStaff = async (req, res) => {
  try {
    const staff = await User.findById(req.params.id);

    if (!staff || staff.hallId.toString() !== req.user.hallId.toString()) {
      return res.json({ error: 'الموظف غير موجود' });
    }

    await User.deleteOne({ _id: staff._id });

    res.json({ message: 'تم حذف الموظف بنجاح' });
  } catch (err) {
    console.error(err);
    res.status(500).send('فشل حذف الموظف');
  }
};


// --- إدارة القوالب ---
exports.listTemplates = async (req, res) => {
  try {
    const templates = await Template.find({ hallId: req.user.hallId });
    res.json({ title: 'إدارة القوالب', templates });
  } catch (err) {
    res.status(500).send('فشل تحميل القوالب');
  }
};

// عرض صفحة إضافة قالب جديد
exports.renderAddTemplate = (req, res) => {
  res.json({ title: 'إضافة قالب' });
};

// إضافة قالب جديد
exports.addTemplate = async (req, res) => {
  try {
    const { name, content } = req.body;

    // تحقق من عدم وجود قالب بنفس الاسم
    const exists = await Template.findOne({ name, hallId: req.user.hallId });
    if (exists) {
      return res.json({ error: 'اسم القالب مستخدم مسبقاً', title: 'إضافة قالب' });
    }

    const template = await Template.create({
      name,
      content,
      hallId: req.user.hallId
    });

    res.json({ message: 'تم إضافة القالب بنجاح', template });
  } catch (err) {
    console.error(err);
    res.status(500).send('فشل في إضافة القالب');
  }
};

// عرض صفحة تعديل قالب
exports.renderEditTemplate = async (req, res) => {
  try {
    const template = await Template.findById(req.params.id);

    if (!template || template.hallId.toString() !== req.user.hallId.toString()) {
      return res.json({ error: 'القالب غير موجود' });
    }

    res.json({ title: 'تم تعديل القالب بنجاح', template });
  } catch (err) {
    console.error(err);
    res.json({ error: 'حدث خطأ أثناء تحميل القالب' });
  }
};

// تعديل قالب
exports.editTemplate = async (req, res) => {
  try {
    const { name, content } = req.body;
    const template = await Template.findById(req.params.id);

    if (!template || template.hallId.toString() !== req.user.hallId.toString()) {
      return res.json({ error: 'القالب غير موجود' });
    }

    // تحقق من عدم وجود قالب بنفس الاسم لقالب آخر
    const existing = await Template.findOne({ name, _id: { $ne: template._id }, hallId: req.user.hallId });
    if (existing) {
      return res.json({
        error: 'اسم القالب مستخدم من قبل قالب آخر',
        title: 'تعديل قالب',
        template: { ...template.toObject(), name, content }
      });
    }

    template.name = name;
    template.content = content;

    await template.save();

    res.json({ message: 'تم تعديل القالب بنجاح', template });
  } catch (err) {
    console.error(err);
    res.status(500).send('فشل تعديل القالب');
  }
};

// حذف قالب
exports.deleteTemplate = async (req, res) => {
  try {
    const template = await Template.findById(req.params.id);

    if (!template || template.hallId.toString() !== req.user.hallId.toString()) {
      return res.json({ error: 'القالب غير موجود' });
    }

    await Template.deleteOne({ _id: template._id });

    res.json({ message: 'تم حذف القالب بنجاح' });
  } catch (err) {
    console.error(err);
    res.status(500).send('فشل حذف القالب');
  }
};

// --- الشكاوى ---
exports.renderHallComplaints = async (req, res) => {
  try {
    const complaints = await Complaint.find({ hallId: req.user.hallId })
      .populate('fromUserId', 'name phone')
      .sort({ createdAt: -1 });
    res.json({ title: 'الشكاوى', complaints });
  } catch (err) {
    res.status(500).send('فشل تحميل الشكاوى');
  }
};

