const { body, validationResult } = require('express-validator');

const contactValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('الاسم يجب أن يكون بين 2-50 حرف')
    .matches(/^[\u0600-\u06FFa-zA-Z\s]+$/)
    .withMessage('الاسم يجب أن يحتوي على أحرف عربية أو إنجليزية فقط'),
  
  body('phone')
    .isPhone()
    .withMessage('رقم الهاتف غير صحيح')
    .normalizePhone(),
  
  body('message')
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('الرسالة يجب أن تكون بين 10-500 حرف')
];

const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).render('pages/contact', {
      title: 'اتصل بنا - موقعنا',
      currentPage: 'contact',
      description: 'تواصل معنا للحصول على المساعدة',
      errors: errors.array(),
      formData: req.body
    });
  }
  next();
};

module.exports = { contactValidation, handleValidationErrors };