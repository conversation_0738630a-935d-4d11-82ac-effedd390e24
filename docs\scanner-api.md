## Scanner Routes (/scanner) - يتطلب role: scanner

### GET /scanner/dashboard
**الوصف:** لوحة تحكم الماسح
**الاستجابة:**
```json
{
  "todayEvents": "array",
  "scannedToday": "number",
  "pendingInvitations": "number"
}
```

### GET /scanner/scan
**الوصف:** صفحة المسح

### GET /scanner/scan/:code
**URL Parameters:** code (string)
**الوصف:** صفحة مسح QR محدد

### GET /scanner/events/:id/invitations
**URL Parameters:** id (ObjectId)
**الوصف:** قائمة دعوات الفعالية
**الاستجابة:**
```json
{
  "event": {
    "id": "ObjectId",
    "eventName": "string",
    "eventDate": "date"
  },
  "invitations": [
    {
      "id": "ObjectId", 
      "guestName": "string",
      "qrCode": "string",
      "isScanned": "boolean",
      "scannedAt": "date"
    }
  ]
}
```

### GET /scanner/verify/:code
**URL Parameters:** code (string)
**الوصف:** التحقق من صحة QR Code
**الاستجابة:**
```json
{
  "valid": "boolean",
  "invitation": {
    "id": "ObjectId",
    "guestName": "string", 
    "eventName": "string",
    "isScanned": "boolean"
  },
  "message": "string"
}
```