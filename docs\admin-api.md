## Admin Routes (/admin) - يتطلب role: admin

### GET /admin/dashboard
**الوصف:** لوحة تحكم الأدمن
**الاستجابة:**
```json
{
  "stats": {
    "totalHalls": "number",
    "totalManagers": "number", 
    "totalEvents": "number",
    "totalClients": "number"
  }
}
```

### GET /admin/users
**الوصف:** قائمة جميع المستخدمين
**Query Parameters:**
- page: number (default: 1)
- limit: number (default: 10)
- role: string (optional filter)

### GET /admin/managers
**الوصف:** قائمة المدراء

### POST /admin/managers/add
**المعاملات المرسلة:**
```json
{
  "name": "string (required)",
  "phone": "string (required)", 
  "password": "string (required)",
  "hallId": "ObjectId (optional)"
}
```

### GET /admin/managers/edit/:id
**URL Parameters:** id (ObjectId)

### POST /admin/managers/edit/:id
**URL Parameters:** id (ObjectId)
**المعاملات المرسلة:**
```json
{
  "name": "string",
  "phone": "string",
  "password": "string (optional)"
}
```

### POST /admin/managers/delete/:id
**URL Parameters:** id (ObjectId)

### GET /admin/halls
**الوصف:** قائمة جميع القاعات

### POST /admin/halls/add
**المعاملات المرسلة:**
```json
{
  "name": "string (required)",
  "location": "string (required)",
  "tables": "number (required)",
  "chairs": "number (required)",
  "defaultPrices": {
    "morning": "number",
    "evening": "number"
  },
  "managerName": "string (required)",
  "managerPhone": "string (required)",
  "managerPassword": "string (required)"
}
```

### GET /admin/templates
**الوصف:** قائمة قوالب الدعوات

### POST /admin/templates/add
**Content-Type:** multipart/form-data
**المعاملات المرسلة:**
```json
{
  "name": "string (required)",
  "description": "string",
  "image": "file (required)",
  "isActive": "boolean"
}
```

### POST /admin/templates/edit/:id
**URL Parameters:** id (ObjectId)
**Content-Type:** multipart/form-data

### POST /admin/templates/delete/:id
**URL Parameters:** id (ObjectId)

### GET /admin/stats
**الوصف:** إحصائيات شاملة
**الاستجابة:**
```json
{
  "users": {
    "total": "number",
    "byRole": {
      "admin": "number",
      "manager": "number", 
      "client": "number",
      "scanner": "number"
    }
  },
  "events": {
    "total": "number",
    "thisMonth": "number",
    "byStatus": {
      "scheduled": "number",
      "cancelled": "number"
    }
  },
  "halls": {
    "total": "number",
    "active": "number"
  }
}
```

### GET /admin/complaints
**الوصف:** قائمة الشكاوى