## Hall Routes (/halls)

### GET /halls
**الوصف:** قائمة جميع القاعات
**Query Parameters:**
- city: string (optional)
- capacity: number (optional)

### GET /halls/:id
**URL Parameters:** id (ObjectId)
**الاستجابة:**
```json
{
  "id": "ObjectId",
  "name": "string",
  "location": "string",
  "tables": "number",
  "chairs": "number",
  "defaultPrices": {
    "morning": "number",
    "evening": "number"
  },
  "generalManager": {
    "name": "string",
    "phone": "string"
  },
  "images": ["string"],
  "amenities": ["string"]
}
```

### GET /halls/:id/availability
**URL Parameters:** id (ObjectId)
**Query Parameters:**
- date: string (YYYY-MM-DD)
**الاستجابة:**
```json
{
  "date": "string",
  "availableSlots": [
    {
      "startTime": "string",
      "endTime": "string",
      "price": "number"
    }
  ],
  "bookedSlots": [
    {
      "startTime": "string", 
      "endTime": "string",
      "eventName": "string"
    }
  ]
}
```