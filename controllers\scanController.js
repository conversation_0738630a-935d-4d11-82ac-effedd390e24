const Invitation = require('../models/Invitation');
const User = require('../models/User');
const Event = require('../models/Event');
const atob = require('atob');

exports.renderDashboard = async (req, res) => {
  const userId = req.user._id;
  const user = await User.findById(userId).populate('hallId', 'name').lean();

  if (!user || !user.hallId) {
    return res.status(404).send("الماسح أو الصالة غير موجودة");
  }

  // جلب المناسبات التي تخص نفس الصالة
  const events = await Event.find({ hallId: user.hallId._id })
    .populate('clientId', 'name') // اسم العميل
    .lean();

  res.json({ user, events, title : 'لوحة الماسح' });
};

exports.scan = async (req, res) => {
  res.json({ title: 'لوحة الماسح'}); // ملف scan.ejs
};

exports.ListInvitations = async (req, res) => {
  const { id } = req.params; // event ID

  try {
    const invitations = await Invitation.find({ eventId: id }).lean();
    res.json( { invitations, title : 'عرض الدعوات' });
  } catch (err) {
    console.error(err);
    res.status(500).send('حدث خطأ أثناء جلب الدعوات');
  }
};

exports.verifyQRCode = async (req, res) => {
  try {
    const { code } = req.params;

    const invitation = await Invitation.findOne({ qrCode: code }).populate('eventId');

    if (!invitation) {
      return res.status(404).json({ success: false, message: 'الدعوة غير موجودة' });
    }

    if (invitation.used) {
      return res.status(400).json({ success: false, message: 'تم استخدام هذه الدعوة مسبقًا' });
    }

    // تفعيل الدعوة
    invitation.used = true;
    await invitation.save();

    res.json({
      success: true,
      message: 'تم التحقق من الدعوة بنجاح',
      data: {
        guestName: invitation.guestName,
        eventName: invitation.eventId?.eventName,
        eventDate: invitation.eventDate,
        numOfPeople: invitation.numOfPeople
      }
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ success: false, message: 'حدث خطأ في التحقق' });
  }
};


exports.scanQRCodePage = async (req, res) => {
  const { code } = req.params;

  try {
    const invitation = await Invitation.findOne({ qrCode: code }).populate('eventId');

    if (!invitation) {
      return res.json({
        status: 'error',
        message: 'الدعوة غير موجودة',
        invitation,
        title : 'نتيجة التحقق'
      });
    }

    if (invitation.used) {
      return res.json({
        status: 'warning',
        message: 'تم استخدام هذه الدعوة مسبقًا',
        invitation,
        title : 'نتيجة التحقق'
      });
    }

    invitation.used = true;
    await invitation.save();

    res.json({
      status: 'success',
      message: `مرحبًا ${invitation.guestName}! دخولك مسموح.`,
      invitation,
      title : 'نتيجة التحقق'
    });
  } catch (err) {
    console.error(err);
    res.json({
      status: 'error',
      message: 'حدث خطأ أثناء التحقق',
      invitation,
      title : 'نتيجة التحقق'
    });
  }
};

/*
exports.verifyQR = async (req, res) => {
  const { qrContent } = req.body;

  try {
    // Extract and decode base64 QR data (skip 'data:image/png;base64,...' part if needed)
    const decoded = atob(qrContent.split(',')[1]);
    const [guestName, numOfPeople, eventDate, clientId] = decoded.split('|');

    const invitation = await Invitation.findOne({ guestName, numOfPeople, eventDate, clientId });
    if (!invitation) return res.status(404).json({ message: 'Invitation not found' });
    if (invitation.used) return res.status(400).json({ message: 'Invitation already used' });

    invitation.used = true;
    await invitation.save();
    res.json({ message: 'QR verified. Entry allowed', guest: guestName });
  } catch (err) {
    res.status(500).json({ message: 'QR verification failed', error: err });
  }
};*/