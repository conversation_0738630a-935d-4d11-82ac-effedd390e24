const User = require('../models/User');
const Hall = require('../models/Hall'); // موديل الصالة
const Template = require('../models/Template'); // موديل القوالب
const Event = require('../models/Event');
const Complaint = require('../models/Complaint');
const Client = require('../models/Client');
const fs = require('fs');
const path = require('path');
// لوحة تحكم المدير العام
exports.dashboard = async (req, res) => {
  try {
    const hallsCount = await Hall.countDocuments();
    const staffCount = await User.countDocuments({ role: 'scanner' });
    const supervisorsCount = await User.countDocuments({ role: 'supervisor' });
    const templatesCount = await Template.countDocuments();

    res.json({
      title: 'لوحة تحكم المدير العام',
      user: req.user,
      stats: {
        halls: hallsCount,
        supervisors: supervisorsCount,
        staff: staffCount,
        templates: templatesCount
      }
    });
  } catch (err) {
    res.status(500).send('خطأ في تحميل لوحة التحكم');
  }
};

// إدارة الصالات والمديرين (عرض)
exports.halls = async (req, res) => {
  try {
    const hall = await Hall.findById(req.user.hallId);
    if (!hall) return res.status(404).json({ message: 'الصالة غير موجودة', error: null });

    res.json({
      title: 'إدارة معلومات الصالة',
      hall,
      error : null
    });
  } catch (err) {
    res.status(500).json({ message: 'خطأ في تحميل بيانات الصالة', error: err });
  }
};


// تعديل معلومات الصالة
exports.updateHall = async (req, res) => {
  const { name, tablesCount, chairsCount, wingTypes, priceDetails } = req.body;

  try {
    const hall = await Hall.findById(req.user.hallId);
    if (!hall) return res.status(404).json({ message: 'الصالة غير موجودة' });

    hall.name = name || hall.name;
    hall.tablesCount = tablesCount || hall.tablesCount;
    hall.chairsCount = chairsCount || hall.chairsCount;
    hall.wingTypes = wingTypes || hall.wingTypes;
    hall.priceDetails = priceDetails || hall.priceDetails;

    await hall.save();

    res.json({
      title: 'إدارة معلومات الصالة',
      hall,
      error : null
    });
  } catch (err) {
    res.status(500).json({
      title: 'إدارة معلومات الصالة',
      error: 'فشل في تحديث بيانات الصالة',
      hall: req.body
    });
  }
};

exports.renderEventsPage = async (req, res) => {
  try {
    const events = await Event.find({ hallId: req.user.hallId })
    .populate('clientId', 'name phone')
    .populate('templateId', 'name imageUrl');  
    const staffList = await User.find({ hallId: req.user.hallId , role : { $in: ['scanner'] } });
    const templates = await Template.find(); // استبدل بـ اسم الموديل الصحيح
    res.json({
      title: 'إدارة الحفلات',
      events,
      staffList,
      templates,
      error : null
    });
  } catch (err) {
    res.status(500).json({ message: 'فشل تحميل الحفلات', error: err });
  }
};

// إضافة حفلة جديدة
exports.addEvent = async (req, res) => {
  // استدعاء الحقول الجديدة
  const { eventDate, startTime, endTime, eventName, eventType, chairsCount, clientName,templateId, phone, password } = req.body;
  const statusWrite = req.body.statusWrite === true;
  try {
    // ابحث عن أي حفلة في نفس التاريخ والقاعه تتقاطع أوقاتها
    const overlappingEvent = await Event.findOne({
      hallId: req.user.hallId,
      eventDate,
      startTime: { $lt: endTime },
      endTime: { $gt: startTime }
    });
    const events = await Event.find({ hallId: req.user.hallId })
    .populate('clientId', 'name phone')
    .populate('templateId', 'name imageUrl');  
    const staffList = await User.find({ hallId: req.user.hallId , role : { $in: ['scanner'] } });
    const templates = await Template.find();
    const countchairs = await Hall.findById(req.user.hallId);
    if (chairsCount > countchairs.chairsCount) {
        
        return res.json({
          title: 'إدارة الحفلات',
          error: 'عدد الكراسي أكبر من العدد المتاح في الصالة',
          events,
          staffList,
          templates,
        });
    }

    if (overlappingEvent) {

      return res.json({
        title: 'إدارة الحفلات',
        error: 'يوجد حفلة أخرى تتقاطع مع نفس الوقت في هذا اليوم',
        events,
        staffList,
        templates,
      });
    }

    // إنشاء العميل
    const client = new Client({
      name: clientName,
      phone,
      password,
      role: 'client',
      hallId: req.user.hallId
    });
    await client.save();

    // إنشاء الحفلة
    const newEvent = new Event({
      hallId: req.user.hallId,
      eventDate,
      startTime,
      endTime,
      eventName,
      eventType,
      numOfPeople : chairsCount,
      clientId: client._id,
      templateId,
      statusWrite
    });

    await newEvent.save();
    res.json({
      title: 'إدارة الحفلات',
      error: null,
      events,
      staffList,
      templates,
    });
  } catch (err) {
    res.status(500).json({ message: 'فشل إضافة الحفلة', error: err });
  }

};

exports.renderEditEvent = async (req, res) => {
  try {
    const event = await Event.findById(req.params.id);
    const templates = await Template.find(); // استبدل بـ اسم الموديل الصحيح
    if (!event) return res.status(404).send('الحفلة غير موجودة');

    res.json({
      title: 'تعديل الحفلة',
      event,
      templates,
      error : null
    });
  } catch (err) {
    res.status(500).json( { message: 'حدث خطأ في جلب الحفلة', error: err });
  }
}

exports.EditEvent = async (req, res) => {
  const { eventName, eventDate, startTime, endTime, numOfPeople, chairsCount, status, templateId} = req.body;
  const statusWrite = req.body.statusWrite === true;
  try {
    const existingEvent = await Event.findById(req.params.id);
    if (!existingEvent) return res.status(404).send('الحفلة غير موجودة');

        // التحقق من وجود القالب
        const selectedTemplate = await Template.findById(templateId);
        if (!selectedTemplate) {
          const events = await Event.find().populate('clientId');
          const templates = await Template.find();
          return res.json({
            title: 'إدارة الحفلات',
            events,
            templates,
            error: 'القالب المختار غير موجود'
          });
        }

    const countchairs = await Hall.findById(req.user.hallId);
    if (chairsCount > countchairs.chairsCount) {
      return res.json({
        title: 'إدارة الحفلات',
        error: 'عدد الكراسي أكبر من العدد المتاح في الصالة',
        events: await Event.find({ hallId: req.user.hallId })
      });
    }
    // التحقق من التداخل مع حفلات أخرى (ليست نفس الحفلة)
    const conflict = await Event.findOne({
      _id: { $ne: req.params.id }, // استبعاد نفس الحفلة
      hallId: existingEvent.hallId,
      eventDate,
      startTime: { $lt: endTime },
      endTime: { $gt: startTime }
    });

    if (conflict) {
      return res.json({
        title: 'تعديل الحفلة',
        event: existingEvent,
        error: 'يوجد تداخل في الوقت مع حفلة أخرى في نفس اليوم.'
      });
    }

    // التحديث
    existingEvent.eventName = eventName;
    existingEvent.eventDate = eventDate;
    existingEvent.startTime = startTime;
    existingEvent.endTime = endTime;
    existingEvent.numOfPeople = numOfPeople;
    existingEvent.status = status;
    existingEvent.templateId = selectedTemplate._id
    existingEvent.statusWrite = statusWrite

    await existingEvent.save();
    res.json({
      title: 'تعديل الحفلة',
      event: existingEvent,
      error: null
    });
  } catch (err) {
    res.status(500).json({ message: 'فشل تعديل الحفلة', error: err });
  }
};

exports.DeleteEvent = async (req, res) => {
  try {
    const event = await Event.findById(req.params.id);
    if (!event) {
      return res.status(404).json({ message: 'الحفلة غير موجودة' });
    }

    await Event.findByIdAndDelete(req.params.id);
    res.json({ message: 'تم حذف الحفلة بنجاح' });
  } catch (err) {
    res.status(500).json({ message: 'فشل حذف الحفلة', error: err });
  }
};

// إدارة الموظفين (عرض)
exports.Staff = async (req, res) => {
  try {
    const hallId = req.user.hallId;
    const staff = await User.find({ hallId, role: { $in: ['manager', 'supervisor', 'scanner'] } }).lean();
    res.json({ title: 'إدارة الموظفين', staff });
  } catch (error) {
    res.status(500).send('خطأ في جلب بيانات الموظفين');
  }
};

// صفحة إضافة موظف جديد
exports.renderAddStaff = (req, res) => {
  res.json({ title: 'إضافة موظف', error: null, formData: {} });
};

// إضافة موظف جديد
exports.addStaff = async (req, res) => {
  const { name, password, role, phone } = req.body;
  try {
    const hallId = req.user.hallId;
    const existing = await User.findOne({ phone });
    if (existing) {
      return res.json({ title: 'إضافة موظف', error: 'البريد الإلكتروني مستخدم بالفعل', formData: req.body });
    }
    const staff = new User({ name, password, role, phone, hallId });
    await staff.save();
    res.json({ title: 'إضافة موظف', error: null, formData: req.body });
  } catch (error) {
    console.log(error);
    res.json({ title: 'إضافة موظف', error: 'فشل في إضافة الموظف', formData: req.body });
  }
};

// صفحة تعديل موظف
exports.renderEditStaff = async (req, res) => {
  try {
    const hallId = req.user.hallId;
    const staff = await User.findOne({ _id: req.params.id, hallId, role: { $in: ['manager', 'supervisor', 'scanner'] } }).lean();
    if (!staff) return res.status(404).send('الموظف غير موجود');
    res.json({ title: 'تعديل موظف', staff, error: null });
  } catch (error) {
    console.log(error);
    res.status(500).send('خطأ في تحميل بيانات الموظف');
  }
};

// تعديل بيانات موظف
exports.editStaff = async (req, res) => {
  const { name, password, role, phone } = req.body;
  try {
    const hallId = req.user.hallId;
    const staff = await User.findOne({ _id: req.params.id, hallId, role: { $in: ['manager', 'supervisor', 'scanner'] } });
    if (!staff) return res.status(404).send('الموظف غير موجود');

    // تحقق إذا البريد الإلكتروني تغير وهل مستخدم من قبل موظف آخر
    if (phone && phone !== staff.phone) {
      const existing = await User.findOne({ phone });
      if (existing) return res.json( { title: 'تعديل موظف', staff, error: 'رقم الهاتف مستخدم بالفعل' });
      staff.phone = phone;
    }

    staff.name = name || staff.name;
    staff.role = role || staff.role;
    staff.phone = phone || staff.phone;

    if (password && password.trim() !== '') {
      staff.password = await bcrypt.hash(password, 10);
    }

    await staff.save();
    res.json({ title: 'تعديل موظف', staff, error: null });
  } catch (error) {
    res.status(500).send('فشل تعديل بيانات الموظف');
  }
};

// حذف موظف
exports.deleteStaff = async (req, res) => {
  try {
    const hallId = req.user.hallId;
    const staff = await User.findOneAndDelete({ _id: req.params.id, hallId, role: { $in: ['manager', 'supervisor', 'scanner'] } });
    if (!staff) return res.status(404).send('الموظف غير موجود');
    res.json({ message: 'تم حذف الموظف بنجاح' });
  } catch (error) {
    res.status(500).send('فشل حذف الموظف');
  }
};

// إدارة القوالب (عرض)
exports.templates = async (req, res) => {
  try {
    const templates = await Template.find({hallId: req.user.hallId});
    res.json({
      title: 'إدارة القوالب',
      templates
    });
  } catch (err) {
    res.status(500).send('خطأ في تحميل القوالب');
  }
};


exports.renderAddTemplate = (req, res) => {
  res.json({ title: 'إضافة قالب', error: null });
};

exports.addTemplate = async (req, res) => {
  try {
    const { templateName } = req.body;
    const imageUrl = req.file ? `/uploads/${req.file.filename}` : '';

    const template = new Template({
      hallId: req.user.hallId,
      templateName,
      imageUrl,
    });

    await template.save();
    res.json({ title: 'إضافة قالب',template ,error: null });
  } catch (err) {
    res.json({ title: 'إضافة قالب', error: 'فشل في إضافة القالب' });
  }
};

exports.renderEditTemplate = async (req, res) => {
  try {
    const template = await Template.findOne({ _id: req.params.id, hallId: req.user.hallId });
    if (!template) return res.status(404).send('القالب غير موجود');
    res.json({ title: 'تعديل قالب', template, error: null });
  } catch (err) {
    res.status(500).send('خطأ في تحميل القالب');
  }
};

exports.editTemplate = async (req, res) => {
  let template = null;
  try {
    const { templateName } = req.body;
    template = await Template.findOne({ _id: req.params.id, hallId: req.user.hallId });

    if (!template) return res.status(404).send('القالب غير موجود');

    template.templateName = templateName;
    if (req.file) {
      if (template.imageUrl) {
        fs.unlink(path.join(__dirname, '..', 'public', template.imageUrl), () => {});
      }
      template.imageUrl = `/uploads/${req.file.filename}`;
    }

    await template.save();
    res.json({ title: 'تعديل قالب', template, error: null });
  } catch (err) {
    console.log(err)
    res.status(500).json({
      template,
      error: 'فشل في تعديل القالب',
      title: 'تعديل القالب'
    });
  }
};

exports.deleteTemplate = async (req, res) => {
  try {
    const template = await Template.findOne({ _id: req.params.id, hallId: req.user.hallId });
    if (!template) return res.status(404).send('القالب غير موجود');

    if (template.imageUrl) {
      fs.unlink(path.join(__dirname, '..', 'public', template.imageUrl), () => {});
    }

    await template.deleteOne();
    res.json({ message: 'تم حذف القالب بنجاح' });
  } catch (err) {
    res.status(500).send('فشل في حذف القالب');
  }
};

exports.getClients = async (req, res) => {
  try {
    // مثال: جلب العملاء مع الحفلات المرتبطة بالصالة (hallId من الجلسة مثلا)
    const hallId = req.user.hallId;

    // جلب العملاء المرتبطين بالصالة (مثلاً عن طريق hallId أو منطق معين حسب تصميمك)
    const clients = await Client.find({ hallId }).lean();

    // جلب الحفلات لكل عميل (يمكن تحسين الأداء لاحقًا)
    for (const client of clients) {
      client.events = await Event.find({ clientId: client._id }).lean();
    }
    res.json({ title: 'إدارة العملاء', clients });
  } catch (error) {
    res.status(500).send('خطأ في جلب بيانات العملاء');
  }
};

exports.renderHallComplaints = async (req, res) => {
  try {
    const hallId = req.user.hallId;

    // جلب الشكاوى الخاصة بهذه الصالة مباشرةً
    const complaints = await Complaint.find({ hallId })
      .populate('fromUserId', 'name phone') // جلب معلومات المستخدم
      .sort({ createdAt: -1 })
      .lean();

    res.json({
      title: 'الشكاوى',
      complaints
    });

  } catch (error) {
    console.error('خطأ في تحميل الشكاوى:', error);
    res.status(500).send('حدث خطأ أثناء تحميل الشكاوى');
  }
};
