## Auth Routes (/auth)

### POST /auth/login
**الوصف:** تسجيل دخول المستخدم
**المعاملات المرسلة:**
```json
{
  "phone": "string (required)",
  "password": "string (required)"
}
```
**الاستجابة:**
```json
{
  "success": true,
  "token": "jwt_token",
  "user": {
    "id": "ObjectId",
    "name": "string",
    "phone": "string",
    "role": "admin|manager|client|scanner"
  }
}
```

### POST /auth/register
**الوصف:** تسجيل مستخدم جديد
**المعاملات المرسلة:**
```json
{
  "name": "string (required)",
  "phone": "string (required)",
  "password": "string (required)",
  "role": "string (optional, default: client)"
}
```

### POST /auth/logout
**الوصف:** تسجيل خروج
**Headers:** Authorization: Bearer {token}