## Event Routes (/events)

### GET /events
**الوصف:** قائمة الفعاليات
**Query Parameters:**
- hallId: ObjectId (optional)
- date: string (optional)
- status: string (optional)

### POST /events
**المعاملات المرسلة:**
```json
{
  "hallId": "ObjectId (required)",
  "eventName": "string (required)",
  "eventDate": "date (required)",
  "startTime": "string (required)",
  "endTime": "string (required)",
  "numOfPeople": "number (required)",
  "clientId": "ObjectId (required)",
  "templateId": "ObjectId (optional)"
}
```

### GET /events/:id
**URL Parameters:** id (ObjectId)
**الاستجابة:**
```json
{
  "id": "ObjectId",
  "eventName": "string",
  "eventDate": "date",
  "startTime": "string",
  "endTime": "string",
  "status": "string",
  "numOfPeople": "number",
  "hall": {
    "name": "string",
    "location": "string"
  },
  "client": {
    "name": "string",
    "phone": "string"
  },
  "template": {
    "name": "string",
    "image": "string"
  },
  "invitations": "number"
}
```

### PUT /events/:id
**URL Parameters:** id (ObjectId)

### DELETE /events/:id
**URL Parameters:** id (ObjectId)

### GET /events/:id/statistics
**URL Parameters:** id (ObjectId)
**الاستجابة:**
```json
{
  "totalInvitations": "number",
  "scannedInvitations": "number",
  "pendingInvitations": "number",
  "attendanceRate": "number"
}
```