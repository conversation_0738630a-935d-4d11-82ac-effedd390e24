const mongoose = require('mongoose');
const User = require('../models/User');

// الاتصال بقاعدة البيانات
const MONGO_URI = process.env.MONGO_URI ;

async function createDefaultAdmin() {
  try {
    console.log('🚀 إنشاء مستخدم أدمن افتراضي...');
    
    // الاتصال بقاعدة البيانات
    await mongoose.connect(MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ تم الاتصال بقاعدة البيانات');

    // التحقق من وجود أدمن مسبقاً
    const existingAdmin = await User.findOne({ role: 'admin' });
    if (existingAdmin) {
      console.log('⚠️ يوجد مستخدم أدمن مسبقاً في النظام');
      console.log(`📱 رقم الهاتف: ${existingAdmin.phone}`);
      return;
    }

    // إنشاء مستخدم أدمن افتراضي
    const defaultAdmin = new User({
      name: 'مدير النظام',
      phone: '0500000000', // يمكن تغييره حسب الحاجة
      password: 'admin123', // كلمة مرور افتراضية - يجب تغييرها
      role: 'admin',
      isActive: true
    });

    await defaultAdmin.save();
    
    console.log('🎉 تم إنشاء مستخدم الأدمن الافتراضي بنجاح!');
    console.log('📋 بيانات تسجيل الدخول:');
    console.log(`📱 رقم الهاتف: ${defaultAdmin.phone}`);
    console.log(`🔑 كلمة المرور: admin123`);
    console.log('⚠️ تأكد من تغيير كلمة المرور بعد أول تسجيل دخول!');

  } catch (error) {
    console.error('❌ خطأ في إنشاء مستخدم الأدمن:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل السكريبت
if (require.main === module) {
  createDefaultAdmin();
}

module.exports = createDefaultAdmin;
