const mongoose = require('mongoose');

const hallSchema = new mongoose.Schema({
  name: { type: String, required: true },
  location: { type: String },
  tables: { type: Number },
  chairs: { type: Number },
  defaultPrices: { type: Number },
  generalManager: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
}, { timestamps: true });

module.exports = mongoose.model('Hall', hallSchema);
