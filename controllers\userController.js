const User = require('../models/User');
const Hall = require('../models/Hall')
const bcrypt = require('bcrypt');

exports.listUsers = async (req, res) => {
  try {
    const users = await User.find().populate('hallId', 'name').lean(); // جلب كل المستخدمين
    res.json({ title: 'قائمة المستخدمين', users });
  } catch (err) {
    res.status(500).send('خطأ في تحميل المدراء');
  }
};

exports.listManagers = async (req, res) => {
  try {
    const managers = await User.find({ role: 'manager' }).populate('hallId', 'name').lean();
    res.json({ managers });
  } catch (err) {
    res.status(500).send('خطأ في تحميل المدراء');
  }
};

exports.renderAddManager = async (req, res) => {
  const halls = await Hall.find();
  res.json({ halls, error: null });
};

exports.addManager = async (req, res) => {
  const { name, phone, password, hallId } = req.body;  // استقبل hallId من النموذج
  try {
    // تأكد من وجود الصالة المرتبطة
    if (!hallId) {
      // جلب الصالات لإعادة عرض النموذج مع الخطأ
      const halls = await Hall.find(); 
      return res.json( { error: 'يرجى اختيار الصالة', halls });
    }

    const existing = await User.findOne({ phone });
    if (existing) {
      const halls = await Hall.find();
      return res.json({ error: 'رقم الهاتف مستخدم بالفعل', halls });
    }

    const manager = new User({ 
      name, 
      phone, 
      password, 
      role: 'manager', 
      hallId 
    });

    await manager.save();
    res.json({ message: 'تم إضافة المدير  بنجاح',manager });
  } catch (err) {
    // جلب الصالات لإعادة عرض النموذج مع الخطأ
    const halls = await Hall.find();
    res.json( { error: 'فشل إضافة المدير', halls });
  }
};

exports.renderEditManager = async (req, res) => {
  try {
    const manager = await User.findById(req.params.id);
    if (!manager || manager.role !== 'manager') return res.status(404).send('المدير غير موجود');

    // جلب جميع الصالات لعرضها في القائمة
    const halls = await Hall.find();

    res.json( { manager, halls ,error: null});
  } catch (err) {
    res.status(500).send('خطأ في تحميل بيانات المدير');
  }
};

exports.editManager = async (req, res) => {
  const { name, phone, password, hallId } = req.body;  // استقبل hallId من النموذج
  try {
    const manager = await User.findById(req.params.id);
    if (!manager || manager.role !== 'manager') return res.status(404).send('المدير غير موجود');

    manager.name = name || manager.name;
    manager.phone = phone || manager.phone;


    if (password && password.trim() !== '') {
      manager.password = password; // سيتم تشفيره تلقائيًا عند الحفظ
    }

    if (hallId) {
      manager.hallId = hallId;
    }

    await manager.save();
    res.json({ message: 'تم تعديل بيانات المدير بنجاح', manager });
  } catch (err) {
    res.status(500).send('فشل تعديل بيانات المدير');
  }
};


exports.listClients = async (req, res) => {
  try {
    const clients = await Client.find({ createdBy: req.user.id });
    res.json( { clients });
  } catch (err) {
    res.status(500).send('خطأ في تحميل العملاء');
  }
};

exports.renderAddClient = (req, res) => {
  res.json({ title: 'إضافة عميل', error: null });
};

exports.addClient = async (req, res) => {
  const { name, phone, password } = req.body;
  try {
    const existing = await Client.findOne({ phone });
    if (existing) return res.json( { error: 'رقم الهاتف مستخدم بالفعل' });
    const client = new Client({ name, phone, password, createdBy: req.user.id });
    await client.save();
    res.json( { message: 'تم إضافة العميل بنجاح', client });
  } catch (err) {
    res.json( { error: 'فشل إضافة العميل' });
  }
};

exports.renderEditClient = async (req, res) => {
  try {
    const client = await Client.findOne({ _id: req.params.id, createdBy: req.user.id });
    if (!client) return res.status(404).send('العميل غير موجود');
    res.json( { client });
  } catch (err) {
    res.status(500).send('خطأ في تحميل بيانات العميل');
  }
};

exports.editClient = async (req, res) => {
  const { name, phone, password } = req.body;
  try {
    const client = await Client.findOne({ _id: req.params.id, createdBy: req.user.id });
    if (!client) return res.status(404).send('العميل غير موجود');
    client.name = name || client.name;
    client.phone = phone || client.phone;
    client.password = password ||  client.password;
    await client.save();
    res.json( { message: 'تم تعديل بيانات العميل بنجاح', client });
  } catch (err) {
    res.status(500).send('فشل تعديل بيانات العميل');
  }
};

exports.deleteClient = async (req, res) => {
  try {
    const client = await Client.findOneAndDelete({ _id: req.params.id, createdBy: req.user.id });
    if (!client) return res.status(404).send('العميل غير موجود');
    res.json( { message: 'تم حذف العميل بنجاح' });
  } catch (err) {
    res.status(500).send('فشل حذف العميل');
  }
};

exports.deleteManager = async (req, res) => {
  try {
    const manager = await User.findOneAndDelete({ _id: req.params.id, role: 'manager' });
    if (!manager) return res.status(404).send('المدير غير موجود');
    res.json( { message: 'تم حذف المدير بنجاح' });
  } catch (err) {
    res.status(500).send('فشل حذف المدير');
  }
}; 