const express = require('express');
const mongoose = require('mongoose');
const dotenv = require('dotenv');
const path = require('path');
const routes = require('./routes');



// Load environment variables first
dotenv.config();

const cookieParser = require('cookie-parser');
const helmet = require('helmet');
const cors = require('cors');
const methodOverride = require('method-override');

const app = express();

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE']
}));
/*
app.use(helmet());/
app.use(cors({
  origin: process.env.CLIENT_URL || 'http://localhost:5000',
  credentials: true
}));*/
app.use(methodOverride('_method'));

// View engine setup
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));
app.use(express.static(path.join(__dirname, 'public')));


  
/** ======== Routes ======== **/
// Main routes loader
app.use('/', routes);

// 404 handler (should be after all routes)
app.use((req, res) => {
    res.status(404).render('404', {
        error: {
            status: 404,
            message: 'الصفحة غير موجودة'
        }
    });
});

// Error handlers

app.use((err, req, res, next) => {
  console.error('Server Error:', err.stack);
  
  // تحديد نوع الخطأ
  if (err.name === 'ValidationError') {
    return res.status(400).json({ 
      message: 'خطأ في البيانات المدخلة',
      errors: err.errors 
    });
  }
  
  if (err.name === 'CastError') {
    return res.status(400).json({ 
      message: 'معرف غير صحيح' 
    });
  }
  
  res.status(500).json({ 
    message: 'حدث خطأ في السيرفر',
    error: process.env.NODE_ENV === 'development' ? err.message : 'خطأ داخلي'
  });
});

// Database connection and server start
const PORT = process.env.PORT || 5000;
const MONGO_URI = process.env.MONGO_URI;

// إعدادات Mongoose محسنة
mongoose.set('strictQuery', false);

mongoose.connect(MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
})
  .then(() => {
    console.log('✅ Connected to MongoDB');
    console.log(`📊 Database: ${mongoose.connection.name}`);
    
    app.listen(PORT, () => {
      console.log(`🚀 Server running on port ${PORT}`);
      console.log(`📱 App URL: http://localhost:${PORT}`);
      console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
    });
  })
  .catch(err => {
    console.error('❌ MongoDB connection failed:', err.message);
    console.error('💡 تأكد من تشغيل MongoDB على النظام');
    process.exit(1);
  });

// معالجة أحداث قاعدة البيانات
mongoose.connection.on('error', (err) => {
  console.error('MongoDB connection error:', err);
});

mongoose.connection.on('disconnected', () => {
  console.warn('⚠️ MongoDB disconnected');
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.error('Unhandled Promise Rejection:', err.message);
  process.exit(1);
});

module.exports = app;
