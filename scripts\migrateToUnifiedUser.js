const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

// الاتصال بقاعدة البيانات
const MONGO_URI = process.env.MONGO_URI;

// تعريف النماذج القديمة
const oldAdminSchema = new mongoose.Schema({
  name: String,
  phone: String,
  password: String,
  isActive: { type: Boolean, default: true }
}, { timestamps: true });

const oldUserSchema = new mongoose.Schema({
  name: String,
  phone: String,
  password: String,
  hallId: { type: mongoose.Schema.Types.ObjectId, ref: 'Hall' },
  role: { type: String, enum: ['manager', 'supervisor', 'scanner'] },
  isActive: { type: Boolean, default: true }
}, { timestamps: true });

const oldClientSchema = new mongoose.Schema({
  name: String,
  phone: String,
  password: String,
  hallId: { type: mongoose.Schema.Types.ObjectId, ref: 'Hall' },
  isActive: { type: Boolean, default: true }
}, { timestamps: true });

// النماذج القديمة
const OldAdmin = mongoose.model('OldAdmin', oldAdminSchema, 'admins');
const OldUser = mongoose.model('OldUser', oldUserSchema, 'users');
const OldClient = mongoose.model('OldClient', oldClientSchema, 'clients');

// النموذج الجديد
const User = require('../models/User');

async function migrateData() {
  try {
    console.log('🚀 بدء عملية الهجرة...');
    
    // الاتصال بقاعدة البيانات
    await mongoose.connect(MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ تم الاتصال بقاعدة البيانات');

    let migratedCount = 0;
    let errorCount = 0;

    // هجرة الأدمن
    console.log('📊 هجرة بيانات الأدمن...');
    const admins = await OldAdmin.find({});
    for (const admin of admins) {
      try {
        // التحقق من عدم وجود المستخدم مسبقاً
        const existingUser = await User.findOne({ phone: admin.phone });
        if (existingUser) {
          console.log(`⚠️ المستخدم ${admin.phone} موجود مسبقاً`);
          continue;
        }

        const newUser = new User({
          name: admin.name,
          phone: admin.phone,
          password: admin.password, // كلمة المرور مشفرة مسبقاً
          role: 'admin',
          isActive: admin.isActive,
          createdAt: admin.createdAt,
          updatedAt: admin.updatedAt
        });

        // تجاوز تشفير كلمة المرور لأنها مشفرة مسبقاً
        await newUser.save({ validateBeforeSave: false });
        migratedCount++;
        console.log(`✅ تم نقل الأدمن: ${admin.name}`);
      } catch (error) {
        console.error(`❌ خطأ في نقل الأدمن ${admin.name}:`, error.message);
        errorCount++;
      }
    }

    // هجرة المستخدمين (المدراء والموظفين)
    console.log('📊 هجرة بيانات المستخدمين...');
    const users = await OldUser.find({});
    for (const user of users) {
      try {
        // التحقق من عدم وجود المستخدم مسبقاً
        const existingUser = await User.findOne({ phone: user.phone });
        if (existingUser) {
          console.log(`⚠️ المستخدم ${user.phone} موجود مسبقاً`);
          continue;
        }

        const newUser = new User({
          name: user.name,
          phone: user.phone,
          password: user.password, // كلمة المرور مشفرة مسبقاً
          role: user.role,
          hallId: user.hallId,
          isActive: user.isActive,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        });

        // تجاوز تشفير كلمة المرور لأنها مشفرة مسبقاً
        await newUser.save({ validateBeforeSave: false });
        migratedCount++;
        console.log(`✅ تم نقل المستخدم: ${user.name} (${user.role})`);
      } catch (error) {
        console.error(`❌ خطأ في نقل المستخدم ${user.name}:`, error.message);
        errorCount++;
      }
    }

    // هجرة العملاء
    console.log('📊 هجرة بيانات العملاء...');
    const clients = await OldClient.find({});
    for (const client of clients) {
      try {
        // التحقق من عدم وجود المستخدم مسبقاً
        const existingUser = await User.findOne({ phone: client.phone });
        if (existingUser) {
          console.log(`⚠️ المستخدم ${client.phone} موجود مسبقاً`);
          continue;
        }

        const newUser = new User({
          name: client.name,
          phone: client.phone,
          password: client.password, // كلمة المرور مشفرة مسبقاً
          role: 'client',
          hallId: client.hallId,
          isActive: client.isActive,
          createdAt: client.createdAt,
          updatedAt: client.updatedAt
        });

        // تجاوز تشفير كلمة المرور لأنها مشفرة مسبقاً
        await newUser.save({ validateBeforeSave: false });
        migratedCount++;
        console.log(`✅ تم نقل العميل: ${client.name}`);
      } catch (error) {
        console.error(`❌ خطأ في نقل العميل ${client.name}:`, error.message);
        errorCount++;
      }
    }

    console.log('\n🎉 انتهت عملية الهجرة!');
    console.log(`✅ تم نقل ${migratedCount} مستخدم بنجاح`);
    console.log(`❌ فشل في نقل ${errorCount} مستخدم`);
    
    // إحصائيات النموذج الجديد
    const totalUsers = await User.countDocuments();
    const adminCount = await User.countDocuments({ role: 'admin' });
    const managerCount = await User.countDocuments({ role: 'manager' });
    const clientCount = await User.countDocuments({ role: 'client' });
    const scannerCount = await User.countDocuments({ role: 'scanner' });
    
    console.log('\n📊 إحصائيات النموذج الجديد:');
    console.log(`👥 إجمالي المستخدمين: ${totalUsers}`);
    console.log(`👑 الأدمن: ${adminCount}`);
    console.log(`🏢 المدراء: ${managerCount}`);
    console.log(`👤 العملاء: ${clientCount}`);
    console.log(`📱 الماسحات: ${scannerCount}`);

  } catch (error) {
    console.error('❌ خطأ في عملية الهجرة:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل السكريبت
if (require.main === module) {
  migrateData();
}

module.exports = migrateData;
