const mongoose = require('mongoose');
const dotenv = require('dotenv');
const bcrypt = require('bcrypt');
const User = require('../models/User');

dotenv.config();

async function createManager() {
  console.log(process.env.MONGO_URI);
  await mongoose.connect(process.env.MONGO_URI);
 // const hashedPassword = await bcrypt.hash('123456', 10);

  const manager = new User({
    name: 'مدير عام',
    phone: '123456',
    password: "123456",
    role: 'admin',
    isActive: true
  });

  await manager.save();
  console.log('✅ Manager created');
  mongoose.disconnect();
}

createManager();
