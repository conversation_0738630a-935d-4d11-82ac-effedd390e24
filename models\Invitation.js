
const mongoose = require('mongoose');

const invitationSchema = new mongoose.Schema({
  clientId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  guestName: { type: String, required: true },
  numOfPeople: { type: Number, required: true },
  eventDate: { type: Date, required: true },
  qrCode: { type: String, unique: true },
  qrCodeImage: { type: String, required: true },
  used: { type: Boolean, default: false },
  eventId: { type: mongoose.Schema.Types.ObjectId, ref: 'Event', required: true }  // الجديد
}, { timestamps: true });

module.exports = mongoose.model('Invitation', invitationSchema);